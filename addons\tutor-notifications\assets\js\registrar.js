(()=>{"use strict";var t={};window.jQuery(document).ready((function(t){var n=wp.i18n,e=n.__,r=n._x,o=n._n,i=n._nx;if(!("serviceWorker"in navigator)){console.warn("Service Worker not supported");return}function a(t,n,e){var r=new Date;r.setTime(r.getTime()+e*24*60*60*1e3);var o="expires="+r.toUTCString();document.cookie=t+"="+n+";"+o+";path="+window._tutorobject.base_path}function u(t){var n=t+"=";var e=document.cookie.split(";");for(var r=0;r<e.length;r++){var o=e[r];while(o.charAt(0)==" "){o=o.substring(1)}if(o.indexOf(n)==0){return o.substring(n.length,o.length)}}return""}function s(t){var n="=".repeat((4-t.length%4)%4);var e=(t+n).replace(/\-/g,"+").replace(/_/g,"/");var r=window.atob(e);var o=new Uint8Array(r.length);for(var i=0;i<r.length;++i){o[i]=r.charCodeAt(i)}return o}function c(t,n,r){navigator.serviceWorker.ready.then((function(o){if(!o.pushManager){a("tutor_pn_dont_ask","yes",365);alert(e("This browser does not support push notification","tutor-pro"));return}o.pushManager.getSubscription().then((function(i){if(i===null){o.pushManager.subscribe({applicationServerKey:t,userVisibleOnly:true}).then((function(t){setTimeout((function(){if(navigator.userAgent.indexOf("Mac OS X")&&n){alert(e("Thanks! Please make sure browser notification is enbled in notification settings.","tutor-pro"))}}),1);r(t,o,true)}))["catch"]((function(t){console.warn(Notification.permission!=="granted"?"PN Permission denied":"PN subscription error")}))}else{r(i,o)}}))}))["catch"]((function(t){console.error("Service Worker error",t)}))}function p(n){c(s(window._tutorobject.tutor_pn_vapid_key),n,(function(n,e,r){e.active.postMessage(JSON.stringify({client_id:window._tutorobject.tutor_pn_client_id,browser_key:u("tutor_pn_browser_key")}));if(window._tutorobject.tutor_pn_client_id==0||!r&&window._tutorobject.tutor_pn_subscription_saved=="yes"){return}t.ajax({url:window._tutorobject.ajaxurl,type:"POST",async:true,data:{action:"tutor_pn_save_subscription",subscription:JSON.stringify(n)}})}))}navigator.serviceWorker.register(window._tutorobject.home_url+"/tutor-push-notification.js").then((function(n){if(Notification.permission=="denied"){return}if(!window._tutorobject.tutor_pn_vapid_key){console.warn("Vapid key could not be generated.");return}if(Notification.permission=="granted"){p();return}var e=t("#tutor-pn-permission");if(e.length&&window._tutorobject.tutor_pn_client_id>0&&!u("tutor_pn_dont_ask")){e.show().css({display:"block"}).animate({bottom:"0px"},1e3);e.find("#tutor-pn-enable").click((function(){p(true)}));e.find("#tutor-pn-dont-ask").click((function(){a("tutor_pn_dont_ask","yes",365)}));e.find("#tutor-pn-enable, #tutor-pn-close, #tutor-pn-dont-ask").click((function(){e.hide()}))}}))["catch"]((function(t){console.warn("Tutor PN Service Worker registration failed",t)}))}))})();